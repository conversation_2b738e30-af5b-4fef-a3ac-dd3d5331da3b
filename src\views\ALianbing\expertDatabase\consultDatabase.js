// 咨询信息模拟数据库
// 用于存储用户提交的咨询信息

// 模拟数据存储
let consultations = [
  {
    id: 1,
    expertId: 1,
    expertName: '王强',
    title: '关于小麦抗旱品种选择的咨询',
    contact: '13800138001',
    content:
      '<p>王教授您好，我是河北地区的农户，想咨询一下在干旱地区适合种植哪些<strong>抗旱性强</strong>的小麦品种？</p><p>我们这里年降水量较少，希望能推荐一些适合的品种。谢谢！</p>',
    contentText:
      '王教授您好，我是河北地区的农户，想咨询一下在干旱地区适合种植哪些抗旱性强的小麦品种？我们这里年降水量较少，希望能推荐一些适合的品种。谢谢！',
    submitTime: '2024-01-15T10:30:00.000Z',
    status: 'replied',
    reply: {
      content:
        '您好！针对干旱地区，我推荐以下几个抗旱性较强的小麦品种：1. 济麦22号 2. 石麦15号 3. 衡观35号。这些品种都具有较强的抗旱能力，适合在年降水量300-500mm的地区种植。',
      replyTime: '2024-01-15T14:20:00.000Z'
    }
  },
  {
    id: 2,
    expertId: 1,
    expertName: '王强',
    title: '玉米施肥技术咨询',
    contact: '<EMAIL>',
    content:
      '<p>专家您好，请问玉米在不同生长期应该如何合理施肥？</p><ul><li>苗期</li><li>拔节期</li><li>抽穗期</li></ul><p>希望能得到详细的指导，谢谢！</p>',
    contentText:
      '专家您好，请问玉米在不同生长期应该如何合理施肥？苗期、拔节期、抽穗期。希望能得到详细的指导，谢谢！',
    submitTime: '2024-01-14T09:15:00.000Z',
    status: 'pending'
  },
  {
    id: 3,
    expertId: 2,
    expertName: '林星照',
    title: '金针菇栽培环境控制问题',
    contact: '15900159001',
    content:
      '<p>林教授您好，我在金针菇栽培过程中遇到了<em>温度和湿度控制</em>的问题。</p><p>请问在不同生长阶段，温度和湿度应该如何调节？</p>',
    contentText:
      '林教授您好，我在金针菇栽培过程中遇到了温度和湿度控制的问题。请问在不同生长阶段，温度和湿度应该如何调节？',
    submitTime: '2024-01-13T16:45:00.000Z',
    status: 'pending'
  }
]

// 生成唯一ID
let nextId = consultations.length + 1

/**
 * 保存咨询信息
 * @param {Object} consultData 咨询数据
 * @returns {Promise<Object>} 保存结果
 */
export const saveConsultation = async (consultData) => {
  try {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 500))

    // 创建新的咨询记录
    const newConsultation = {
      id: nextId++,
      ...consultData,
      submitTime: new Date().toISOString(),
      status: 'pending'
    }

    // 添加到数据存储
    consultations.unshift(newConsultation)

    // 模拟保存到本地存储
    localStorage.setItem('expertConsultations', JSON.stringify(consultations))

    console.log('新增咨询记录:', newConsultation)

    return {
      success: true,
      data: newConsultation,
      message: '咨询提交成功'
    }
  } catch (error) {
    console.error('保存咨询信息失败:', error)
    return {
      success: false,
      message: '保存失败，请重试'
    }
  }
}

/**
 * 获取所有咨询信息
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 咨询列表
 */
export const getConsultations = async (params = {}) => {
  try {
    // 从本地存储加载数据
    const stored = localStorage.getItem('expertConsultations')
    if (stored) {
      consultations = JSON.parse(stored)
    }

    let result = [...consultations]

    // 按专家ID筛选
    if (params.expertId) {
      result = result.filter((item) => item.expertId === params.expertId)
    }

    // 按状态筛选
    if (params.status) {
      result = result.filter((item) => item.status === params.status)
    }

    // 按关键词搜索
    if (params.keyword) {
      const keyword = params.keyword.toLowerCase()
      result = result.filter(
        (item) =>
          item.title.toLowerCase().includes(keyword) ||
          item.contentText.toLowerCase().includes(keyword)
      )
    }

    // 排序（最新的在前）
    result.sort((a, b) => new Date(b.submitTime) - new Date(a.submitTime))

    // 分页
    const page = params.page || 1
    const pageSize = params.pageSize || 10
    const start = (page - 1) * pageSize
    const end = start + pageSize

    return {
      success: true,
      data: {
        list: result.slice(start, end),
        total: result.length,
        page,
        pageSize
      }
    }
  } catch (error) {
    console.error('获取咨询信息失败:', error)
    return {
      success: false,
      message: '获取数据失败'
    }
  }
}

/**
 * 根据ID获取咨询详情
 * @param {number} id 咨询ID
 * @returns {Promise<Object>} 咨询详情
 */
export const getConsultationById = async (id) => {
  try {
    const stored = localStorage.getItem('expertConsultations')
    if (stored) {
      consultations = JSON.parse(stored)
    }

    const consultation = consultations.find((item) => item.id === id)

    if (consultation) {
      return {
        success: true,
        data: consultation
      }
    } else {
      return {
        success: false,
        message: '咨询记录不存在'
      }
    }
  } catch (error) {
    console.error('获取咨询详情失败:', error)
    return {
      success: false,
      message: '获取详情失败'
    }
  }
}

/**
 * 专家回复咨询
 * @param {number} id 咨询ID
 * @param {string} replyContent 回复内容
 * @returns {Promise<Object>} 回复结果
 */
export const replyConsultation = async (id, replyContent) => {
  try {
    const stored = localStorage.getItem('expertConsultations')
    if (stored) {
      consultations = JSON.parse(stored)
    }

    const index = consultations.findIndex((item) => item.id === id)

    if (index !== -1) {
      consultations[index].status = 'replied'
      consultations[index].reply = {
        content: replyContent,
        replyTime: new Date().toISOString()
      }

      // 保存到本地存储
      localStorage.setItem('expertConsultations', JSON.stringify(consultations))

      return {
        success: true,
        data: consultations[index],
        message: '回复成功'
      }
    } else {
      return {
        success: false,
        message: '咨询记录不存在'
      }
    }
  } catch (error) {
    console.error('回复咨询失败:', error)
    return {
      success: false,
      message: '回复失败，请重试'
    }
  }
}

/**
 * 删除咨询记录
 * @param {number} id 咨询ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteConsultation = async (id) => {
  try {
    const stored = localStorage.getItem('expertConsultations')
    if (stored) {
      consultations = JSON.parse(stored)
    }

    const index = consultations.findIndex((item) => item.id === id)

    if (index !== -1) {
      consultations.splice(index, 1)

      // 保存到本地存储
      localStorage.setItem('expertConsultations', JSON.stringify(consultations))

      return {
        success: true,
        message: '删除成功'
      }
    } else {
      return {
        success: false,
        message: '咨询记录不存在'
      }
    }
  } catch (error) {
    console.error('删除咨询失败:', error)
    return {
      success: false,
      message: '删除失败，请重试'
    }
  }
}

/**
 * 获取咨询统计信息
 * @returns {Promise<Object>} 统计信息
 */
export const getConsultationStats = async () => {
  try {
    const stored = localStorage.getItem('expertConsultations')
    if (stored) {
      consultations = JSON.parse(stored)
    }

    const total = consultations.length
    const pending = consultations.filter((item) => item.status === 'pending').length
    const replied = consultations.filter((item) => item.status === 'replied').length
    const closed = consultations.filter((item) => item.status === 'closed').length

    // 按专家统计
    const expertStats = {}
    consultations.forEach((item) => {
      if (!expertStats[item.expertId]) {
        expertStats[item.expertId] = {
          expertName: item.expertName,
          total: 0,
          pending: 0,
          replied: 0
        }
      }
      expertStats[item.expertId].total++
      if (item.status === 'pending') expertStats[item.expertId].pending++
      if (item.status === 'replied') expertStats[item.expertId].replied++
    })

    return {
      success: true,
      data: {
        total,
        pending,
        replied,
        closed,
        expertStats: Object.values(expertStats)
      }
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
    return {
      success: false,
      message: '获取统计信息失败'
    }
  }
}

// 导出默认数据（用于初始化）
export const defaultConsultations = consultations
