<template>
  <el-dialog
    v-model="visible"
    title="历史咨询"
    width="800px"
    :before-close="handleClose"
    class="history-messages-dialog"
  >
    <div class="history-container">
      <!-- 搜索区域 -->
      <div class="search-section">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索咨询内容..."
          clearable
          prefix-icon="Search"
          @input="handleSearch"
          style="width: 300px"
        />
      </div>

      <!-- 历史记录列表 -->
      <div class="history-list" v-loading="loading">
        <div v-for="(item, index) in filteredHistoryList" :key="item.id" class="history-item">
          <div class="item-header">
            <div class="item-number">{{ index + 1 }}</div>
            <div class="item-avatar">
              <img :src="item.expertAvatar || defaultAvatar" :alt="item.expertName" />
            </div>
            <div class="item-content">
              <div class="question-title">{{ item.question }}</div>
              <div class="expert-info">
                <span class="expert-name">咨询专家：{{ item.expertName }}</span>
                <span class="expert-title">{{ item.expertTitle }}</span>
              </div>
              <div class="reply-status">
                <span :class="['status-badge', item.replyStatus]">
                  {{ getStatusText(item.replyStatus) }}
                </span>
              </div>
            </div>
            <div class="item-time">{{ item.consultTime }}</div>
            <div class="close-btn" @click="removeHistoryItem(item.id)">
              <el-icon><Close /></el-icon>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredHistoryList.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无历史咨询记录" />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="danger" @click="clearAllHistory">清空历史</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { Close } from '@element-plus/icons-vue'

export default {
  name: 'HistoryMessages',
  components: {
    Close
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close'],
  data() {
    return {
      loading: false,
      searchKeyword: '',
      defaultAvatar: '/src/assets/imgs/avatar.jpg',
      // 模拟历史咨询数据
      historyList: [
        {
          id: 1,
          question: '蘑菇培养基易生杂菌怎么办？',
          expertName: '陈知道',
          expertTitle: '副教授',
          expertAvatar: '/src/assets/imgs/avatar.jpg',
          consultTime: '2025年12月09日 10:07',
          replyStatus: 'replied' // replied, waiting, expired
        },
        {
          id: 2,
          question: '玉米茎基腐病怎么防治？',
          expertName: '林星眠',
          expertTitle: '教授',
          expertAvatar: '/src/assets/imgs/avatar.jpg',
          consultTime: '2025年10月21日 15:50',
          replyStatus: 'waiting'
        },
        {
          id: 3,
          question: '小麦面筋含量怎么提高？',
          expertName: '陈硕舒',
          expertTitle: '讲师',
          expertAvatar: '/src/assets/imgs/avatar.jpg',
          consultTime: '2025年9月05日 08:23',
          replyStatus: 'waiting'
        },
        {
          id: 4,
          question: '香菇出菇少原因？',
          expertName: '李徐宁',
          expertTitle: '讲师',
          expertAvatar: '/src/assets/imgs/avatar.jpg',
          consultTime: '2025年8月16日 20:48',
          replyStatus: 'replied'
        },
        {
          id: 5,
          question: '玉米倒伏怎样提前预防？',
          expertName: '赵清和',
          expertTitle: '副教授',
          expertAvatar: '/src/assets/imgs/avatar.jpg',
          consultTime: '2025年7月28日 13:19',
          replyStatus: 'waiting'
        }
      ]
    }
  },
  computed: {
    filteredHistoryList() {
      if (!this.searchKeyword) {
        return this.historyList
      }
      return this.historyList.filter(
        (item) =>
          item.question.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
          item.expertName.toLowerCase().includes(this.searchKeyword.toLowerCase())
      )
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },

    handleSearch() {
      // 搜索逻辑已在computed中实现
    },

    getStatusText(status) {
      const statusMap = {
        replied: '已回复',
        waiting: '待回复',
        expired: '已过期'
      }
      return statusMap[status] || '未知状态'
    },

    removeHistoryItem(id) {
      this.$confirm('确定要删除这条历史记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.historyList = this.historyList.filter((item) => item.id !== id)
          this.$message.success('删除成功')
        })
        .catch(() => {
          // 用户取消删除
        })
    },

    clearAllHistory() {
      this.$confirm('确定要清空所有历史记录吗？此操作不可恢复！', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.historyList = []
          this.$message.success('历史记录已清空')
        })
        .catch(() => {
          // 用户取消清空
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.history-messages-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
    max-height: 600px;
    overflow-y: auto;
  }
}

.history-container {
  .search-section {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-start;
  }

  .history-list {
    .history-item {
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      margin-bottom: 16px;
      background: #fff;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-color: #4285f4;
      }

      .item-header {
        display: flex;
        align-items: center;
        padding: 20px;
        position: relative;

        .item-number {
          width: 30px;
          height: 30px;
          background: #4285f4;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          font-size: 14px;
          margin-right: 15px;
          flex-shrink: 0;
        }

        .item-avatar {
          margin-right: 15px;
          flex-shrink: 0;

          img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #f0f0f0;
          }
        }

        .item-content {
          flex: 1;
          min-width: 0;

          .question-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
          }

          .expert-info {
            margin-bottom: 8px;

            .expert-name {
              color: #666;
              font-size: 14px;
              margin-right: 10px;
            }

            .expert-title {
              color: #999;
              font-size: 12px;
              background: #f5f5f5;
              padding: 2px 8px;
              border-radius: 10px;
            }
          }

          .reply-status {
            .status-badge {
              padding: 4px 12px;
              border-radius: 12px;
              font-size: 12px;
              font-weight: 500;

              &.replied {
                background: #f6ffed;
                color: #52c41a;
                border: 1px solid #b7eb8f;
              }

              &.waiting {
                background: #fff7e6;
                color: #fa8c16;
                border: 1px solid #ffd591;
              }

              &.expired {
                background: #fff2f0;
                color: #ff4d4f;
                border: 1px solid #ffccc7;
              }
            }
          }
        }

        .item-time {
          color: #999;
          font-size: 12px;
          margin-right: 15px;
          flex-shrink: 0;
          text-align: right;
          line-height: 1.4;
        }

        .close-btn {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background: #f5f5f5;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          flex-shrink: 0;

          &:hover {
            background: #ff4d4f;
            color: white;
          }

          .el-icon {
            font-size: 14px;
          }
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px 0;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
}

// 响应式设计
@media (max-width: 768px) {
  .history-messages-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 5vh auto;
    }
  }

  .history-container {
    .search-section {
      :deep(.el-input) {
        width: 100% !important;
      }
    }

    .history-list {
      .history-item {
        .item-header {
          padding: 15px;
          flex-wrap: wrap;

          .item-number {
            width: 25px;
            height: 25px;
            font-size: 12px;
          }

          .item-avatar {
            img {
              width: 40px;
              height: 40px;
            }
          }

          .item-content {
            .question-title {
              font-size: 14px;
            }
          }

          .item-time {
            font-size: 11px;
            margin-right: 10px;
          }

          .close-btn {
            width: 20px;
            height: 20px;

            .el-icon {
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}
</style>
